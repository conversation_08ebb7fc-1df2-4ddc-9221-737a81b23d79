import os
import yaml
from dotenv import load_dotenv
from langchain_community.vectorstores import Chroma
from langchain_openai import OpenAIEmbeddings
from langchain.schema import Document
import glob

# Load environment variables from .env
load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise RuntimeError("OPENAI_API_KEY not set in .env file!")

# Helper to segment YAML files
def load_and_segment_yaml(path, doc_name):
    with open(path, 'r', encoding='utf-8') as f:
        data = yaml.safe_load(f)
    segments = []
    for key, value in data.items():
        if isinstance(value, dict):
            for subkey, subval in value.items():
                section_text = f"{doc_name} {key} - {subkey}:\n{yaml.dump(subval, default_flow_style=False)}"
                segments.append(Document(page_content=section_text, metadata={"name": doc_name, "section": f"{key}-{subkey}"}))
        elif isinstance(value, list):
            section_text = f"{doc_name} {key}: {', '.join(str(i) for i in value)}"
            segments.append(Document(page_content=section_text, metadata={"name": doc_name, "section": key}))
        else:
            section_text = f"{doc_name} {key}: {value}"
            segments.append(Document(page_content=section_text, metadata={"name": doc_name, "section": key}))
    return segments

# Map folder to collection name
FOLDER_TO_COLLECTION = {
    'characters': 'characters',
    'monsters': 'monsters',
    'items': 'items',
}

# Base directory
base_dir = os.getcwd()
embeddings = OpenAIEmbeddings(openai_api_key=OPENAI_API_KEY)
persist_dir = ".chroma_langchain"

# For each collection type, load and insert documents
for folder, collection_name in FOLDER_TO_COLLECTION.items():
    folder_path = os.path.join(base_dir, folder)
    if not os.path.exists(folder_path):
        continue
    all_segments = []
    for file_path in glob.glob(os.path.join(folder_path, "**", "*.yaml"), recursive=True):
        doc_name = os.path.splitext(os.path.basename(file_path))[0]
        all_segments.extend(load_and_segment_yaml(file_path, doc_name))
    if all_segments:
        print(f"Adding {len(all_segments)} docs to collection '{collection_name}'...")
        Chroma.from_documents(
            all_segments,
            embeddings,
            collection_name=collection_name,
            persist_directory=persist_dir
        )
    else:
        print(f"No documents found for collection '{collection_name}'.")

print("Collections created and populated.")

import chromadb
from chromadb.config import Settings
import yaml
import os
import openai
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables from .env
load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise RuntimeError("OPENAI_API_KEY not set in .env file!")
openai.api_key = OPENAI_API_KEY
openai_client = OpenAI(api_key=OPENAI_API_KEY)

# Load and segment character sheet YAML into sections and sub-sections

def load_and_segment_character_sheet(path, character_name):
    with open(path, 'r', encoding='utf-8') as f:
        data = yaml.safe_load(f)
    segments = []
    for key, value in data.items():
        # For nested dicts (like inventory), store each sub-section as its own document
        if isinstance(value, dict):
            for subkey, subval in value.items():
                section_text = f"{character_name} {key} - {subkey}:\n{yaml.dump(subval, default_flow_style=False)}"
                segments.append({
                    "document": section_text,
                    "metadata": {"character": character_name, "section": f"{key}-{subkey}"}
                })
        elif isinstance(value, list):
            section_text = f"{character_name} {key}: {', '.join(str(i) for i in value)}"
            segments.append({
                "document": section_text,
                "metadata": {"character": character_name, "section": key}
            })
        else:
            section_text = f"{character_name} {key}: {value}"
            segments.append({
                "document": section_text,
                "metadata": {"character": character_name, "section": key}
            })
    return segments

# Paths to character sheets
dir_path = os.path.join(os.getcwd(), 'characters')
aragorn_path = os.path.join(dir_path, 'aragorn', 'sheet.yaml')
elowen_path = os.path.join(dir_path, 'elowen', 'sheet.yaml')

aragorn_segments = load_and_segment_character_sheet(aragorn_path, "Aragorn")
elowen_segments = load_and_segment_character_sheet(elowen_path, "Elowen")

# Combine all segments
documents = [s["document"] for s in aragorn_segments + elowen_segments]
ids = [f"{s['metadata']['character'].lower()}_{s['metadata']['section']}" for s in aragorn_segments + elowen_segments]
metadatas = [s["metadata"] for s in aragorn_segments + elowen_segments]

# Create a Chroma client (using default settings, stores data in-memory)
client = chromadb.Client(Settings())

# Create a collection for D&D character sheet sections
collection = client.create_collection(name="dnd_character_sections")

# Add all segments to the collection
collection.add(
    documents=documents,
    ids=ids,
    metadatas=metadatas
)

# List of all possible sections for LLM mapping
def get_all_sections():
    return [
        "strength", "dexterity", "constitution", "intelligence", "wisdom", "charisma", "hit_points", "armor_class",
        "proficiencies", "skills", "languages", "features", "inventory-weapons", "inventory-armor", "inventory-items",
        "spells", "notes", "background", "level", "class", "race", "alignment", "name"
    ]

# Use OpenAI LLM to extract character and section from query
def extract_character_and_section(query):
    sections = get_all_sections()
    characters = ["Aragorn", "Elowen"]
    prompt = f"""
Given the user query: '{query}'
Choose the most likely character and section from these lists:
Characters: {characters}
Sections: {sections}
Respond in JSON: {{\"character\": <character>, \"section\": <section>}}
"""
    response = openai_client.chat.completions.create(
        model="gpt-4.1-mini-2025-04-14",
        messages=[{"role": "user", "content": prompt}],
        max_tokens=50,
        temperature=0
    )
    import json
    try:
        content = response.choices[0].message.content
        parsed = json.loads(content)
        return parsed.get("character"), parsed.get("section")
    except Exception:
        return None, None

try:
    print("Type your question about the characters and press Enter (Ctrl+C to exit):")
    while True:
        q = input("Query: ")
        if not q.strip():
            print("Please enter a question.")
            continue
        filter_character, filter_section = extract_character_and_section(q)
        print(f"[DEBUG] LLM extracted character: {filter_character}, section: {filter_section}")
        results = collection.query(query_texts=[q], n_results=10)
        print("\nMost relevant sections (AI filtered):\n")
        found = False
        for doc, meta in zip(results["documents"][0], results["metadatas"][0]):
            if filter_section and meta["section"] != filter_section:
                continue
            if filter_character and meta["character"] != filter_character:
                continue
            print(f"Character: {meta['character']} | Section: {meta['section']}")
            print(doc)
            print("---")
            found = True
        if not found:
            print("No exact section match found. Here are the top results:")
            for doc, meta in zip(results["documents"][0], results["metadatas"][0]):
                print(f"Character: {meta['character']} | Section: {meta['section']}")
                print(doc)
                print("---")
        print()
except KeyboardInterrupt:
    print("\nExiting.")

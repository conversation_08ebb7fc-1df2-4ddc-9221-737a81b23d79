/* D&D Solo Flask System - Modern UI Styles */
@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@700&display=swap');

body {
  background: #18181b;
  color: #e5e7eb;
  font-family: 'Segoe UI', Arial, sans-serif;
  margin: 0;
  min-height: 100vh;
}

.container {
  max-width: 72rem;
  margin: 0 auto;
  padding: 2.5rem 1rem 4rem 1rem;
}

h1, h2, h3, .brand {
  font-family: 'Cinzel', serif;
  font-weight: 700;
  letter-spacing: 0.03em;
}

h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5em;
}

h2 {
  font-size: 1.5rem;
  margin-bottom: 1em;
}

.breadcrumbs {
  display: flex;
  gap: 0.5em;
  align-items: center;
  font-size: 0.95em;
  color: #94a3b8;
  margin-bottom: 2em;
}
.breadcrumbs a {
  color: #818cf8;
  text-decoration: none;
  transition: color 0.2s;
}
.breadcrumbs a:hover {
  color: #fbbf24;
}
.breadcrumbs .chevron {
  color: #64748b;
}

.form-section {
  background: #23272a;
  border-radius: 1.25rem;
  padding: 2rem 2.5rem;
  margin-bottom: 2.5rem;
  box-shadow: 0 2px 12px #0003;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}
@media (max-width: 768px) {
  .form-grid { grid-template-columns: 1fr; }
}
.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5em;
}
label {
  color: #cbd5e1;
  font-weight: 500;
  margin-bottom: 0.2em;
}
input, select {
  background: #18181b;
  color: #f1f5f9;
  border: 1px solid #334155;
  border-radius: 0.5em;
  padding: 0.7em 1em;
  font-size: 1em;
  outline: none;
  transition: border 0.2s;
}
input:focus, select:focus {
  border-color: #818cf8;
}

.adv-toggle {
  display: flex;
  gap: 0.5em;
  margin-top: 0.2em;
}
.adv-btn {
  flex: 1;
  background: #23272a;
  color: #a5b4fc;
  border: 1px solid #6366f1;
  border-radius: 0.5em;
  padding: 0.5em 0;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.adv-btn.selected, .adv-btn:hover {
  background: #6366f1;
  color: #fff;
}

.primary-btn {
  background: #6366f1;
  color: #fff;
  font-weight: 600;
  border-radius: 0.75em;
  box-shadow: 0 2px 8px #0002;
  padding: 0.75em 2em;
  border: none;
  cursor: pointer;
  font-size: 1.1em;
  margin-top: 1.5em;
  transition: background 0.2s;
}
.primary-btn:hover {
  background: #4f46e5;
}

.result-modal {
  position: fixed;
  inset: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: start;
  justify-content: center;
  z-index: 1000;
  padding-top: 4vh;
}
.result-card {
  background: #1e1e1e;
  border-radius: 2rem;
  box-shadow: 0 8px 32px #0008;
  max-width: 28rem;
  width: 100%;
  padding: 2.5rem 2rem 2rem 2rem;
  color: #f1f5f9;
  position: relative;
}
.outcome-bar {
  height: 8px;
  border-radius: 8px 8px 0 0;
  margin-bottom: 1.5em;
}
.outcome-success { background: #50fa7b; }
.outcome-failure { background: #ef4444; }
.outcome-critical { background: #ffd700; }

.result-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 8px;
  font-size: 1.1em;
  margin-bottom: 1.5em;
}
.result-table td {
  padding: 8px 12px;
}
.result-table .label {
  color: #8be9fd;
  width: 40%;
}
.result-table .value {
  color: #f1f5f9;
}
.result-table .outcome-row {
  background: #44475a;
  border-radius: 8px;
}
.result-table .outcome-label {
  color: #f1fa8c;
}
.result-table .outcome-value {
  font-weight: bold;
}

.narration-block {
  background: #23272a;
  padding: 1.5em 1.5em;
  border-left: 4px solid #ffd700;
  border-radius: 8px;
  font-style: italic;
  color: #f1fa8c;
  font-size: 1.15em;
  white-space: pre-line;
  margin-bottom: 1em;
}
.copy-btn {
  float: right;
  background: none;
  border: 1px solid #818cf8;
  color: #a5b4fc;
  border-radius: 0.5em;
  padding: 0.4em 1.2em;
  font-size: 1em;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.copy-btn:hover {
  background: #6366f1;
  color: #fff;
}
.toast {
  position: fixed;
  top: 2.5em;
  right: 2.5em;
  background: #23272a;
  color: #f1fa8c;
  padding: 0.8em 1.5em;
  border-radius: 0.7em;
  box-shadow: 0 2px 8px #0005;
  font-size: 1.1em;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
  z-index: 2000;
}
.toast.show { opacity: 1; pointer-events: auto; }

@media (max-width: 600px) {
  .form-section, .result-card { padding: 1.2rem 0.5rem; }
  .container { padding: 1.2rem 0.2rem 2rem 0.2rem; }
}

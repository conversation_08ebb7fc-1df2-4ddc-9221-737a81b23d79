import os
import glob
import yaml
from flask import Flask, render_template_string, request, redirect, url_for, flash
from scene_oracle_builder import ENVIRONMENTS, MOODS, OBSTACLES, NPC_TYPES, GOALS, oracle_roll, clean_ai_yaml
from chromadb_scene_rag import get_character_stat_and_modifier, interpret_action as base_interpret_action, load_scene_context, roll_dice, narrate_result
from langchain_openai import Chat<PERSON><PERSON><PERSON><PERSON>
from openai import OpenAI
from dotenv import load_dotenv

load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise RuntimeError("OPENAI_API_KEY not set in .env file!")
llm = ChatOpenAI(openai_api_key=OPENAI_API_KEY, model_name="gpt-4_1-mini-2025-04-14", temperature=0)

app = Flask(__name__)
app.secret_key = 'supersecretkey'  # For flash messages

SCENE_DIR = '.'

# --- Templates ---
layout = '''
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>D&D Solo Flask System</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@700&display=swap" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: { cinzel: ['Cinzel', 'serif'] },
          colors: {
            dndgold: '#ffd700',
            dndred: '#ef4444',
            dndgreen: '#50fa7b',
            dndnight: '#18181b',
            dndslate: '#23272a',
          }
        }
      }
    }
  </script>
</head>
<body class="bg-dndnight min-h-screen">
  <div class="max-w-4xl mx-auto px-4 py-10">
    <nav class="flex gap-2 text-slate-400 text-sm mb-8 items-center">
      <a href="/" class="hover:text-indigo-400">Scene</a>
      <span class="mx-1">›</span>
      <a href="/scene_builder" class="hover:text-indigo-400">Scene Builder</a>
      <span class="mx-1">›</span>
      <a href="/action" class="hover:text-indigo-400">D&D Action</a>
    </nav>
    <h1 class="font-cinzel text-5xl font-bold leading-tight text-center text-dndgold mb-10">D&D Solo Flask System</h1>
    {{ body|safe }}
  </div>
  <div id="toast" class="fixed bottom-6 right-6 bg-indigo-600 text-white px-4 py-2 rounded-lg shadow-md text-lg z-50 hidden">Copied!</div>
  <script>
    function copyNarration() {
      const narration = document.querySelector('.narration-block');
      if (narration) {
        navigator.clipboard.writeText(narration.innerText);
        const toast = document.getElementById('toast');
        toast.classList.remove('hidden');
        setTimeout(() => toast.classList.add('hidden'), 1200);
      }
    }
    document.addEventListener('DOMContentLoaded', function() {
      document.querySelectorAll('.adv-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
          document.querySelectorAll('.adv-btn').forEach(b => b.classList.remove('bg-indigo-500','text-white'));
          btn.classList.add('bg-indigo-500','text-white');
          document.getElementById('adv_type').value = btn.dataset.value;
        });
      });
    });
  </script>
</body>
</html>
'''

@app.route('/')
def index():
    scene_files = sorted(glob.glob(os.path.join(SCENE_DIR, 'scene_*.yaml')))
    body = '<h2 class="text-3xl font-bold mb-4">Available Scenes</h2><ul class="list-disc list-inside mb-6">'
    for f in scene_files:
        name = os.path.basename(f)
        body += f'<li><a href="/scene/{name}" class="text-indigo-400 hover:underline">{name}</a></li>'
    body += '</ul>'
    body += '<a href="/build" class="inline-block bg-indigo-600 text-white px-4 py-2 rounded-lg shadow-md hover:bg-indigo-500">[+ Create New Scene]</a>'
    return render_template_string(layout, body=body)

@app.route('/scene/<filename>', methods=['GET', 'POST'])
def view_scene(filename):
    path = os.path.join(SCENE_DIR, filename)
    if request.method == 'POST':
        # Save edited YAML
        new_yaml = request.form['scene_yaml']
        try:
            yaml.safe_load(new_yaml)
            with open(path, 'w', encoding='utf-8') as f:
                f.write(new_yaml)
            flash('Scene saved!')
        except Exception as e:
            flash(f'YAML Error: {e}')
    with open(path, 'r', encoding='utf-8') as f:
        content = f.read()
    body = f'''
    <h2 class="text-3xl font-bold mb-4">Scene: {filename}</h2>
    <form method="post" class="mb-6">
      <textarea name="scene_yaml" rows="20" class="w-full p-4 rounded-lg border-2 border-dndslate bg-dndnight text-white" placeholder="YAML content...">{content}</textarea>
      <br>
      <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded-lg shadow-md hover:bg-indigo-500">Save</button>
    </form>
    <a href="/" class="text-indigo-400 hover:underline">&larr; Back to Scenes</a>
    '''
    return render_template_string(layout, body=body)

@app.route('/build', methods=['GET', 'POST'])
def build_scene():
    if request.method == 'POST':
        env = request.form.get('env')
        mood = request.form.get('mood')
        obstacle = request.form.get('obstacle')
        npc = request.form.get('npc')
        goal = request.form.get('goal')
        extra = request.form.get('extra', '')
        prompt = f"""
You are a D&D scene builder. Given the following elements, write a YAML scene file in the same schema as the user's system. Include all relevant fields and a vivid description. Use the following:
- Environment: {env}
- Mood: {mood}
- Obstacle: {obstacle}
- NPC: {npc}
- Goal: {goal}
- Extra: {extra}
Respond ONLY with valid YAML, no explanation.
"""
        ai_yaml = llm.invoke(prompt).content
        ai_yaml_clean = clean_ai_yaml(ai_yaml)
        try:
            scene_dict = yaml.safe_load(ai_yaml_clean)
            filename = f"scene_{env.lower().replace(' ','_')}.yaml"
            with open(os.path.join(SCENE_DIR, filename), 'w', encoding='utf-8') as f:
                yaml.dump(scene_dict, f, allow_unicode=True, sort_keys=False)
            flash(f'Scene {filename} created!')
            return redirect(url_for('view_scene', filename=filename))
        except Exception as e:
            flash(f'YAML Error: {e}')
            return render_template_string(layout, body=f'<pre>{ai_yaml_clean}</pre>')
    # GET: show form
    def options(table):
        return ''.join([f'<option value="{x}">{x}</option>' for x in table])
    body = f'''
    <h2 class="text-3xl font-bold mb-4">Build New Scene</h2>
    <form method="post" class="mb-6">
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-1">Environment</label>
          <select name="env" class="block w-full p-2 rounded-lg border-2 border-dndslate bg-dndnight text-white">
            {options(ENVIRONMENTS)}
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-1">Mood</label>
          <select name="mood" class="block w-full p-2 rounded-lg border-2 border-dndslate bg-dndnight text-white">
            {options(MOODS)}
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-1">Obstacle</label>
          <select name="obstacle" class="block w-full p-2 rounded-lg border-2 border-dndslate bg-dndnight text-white">
            {options(OBSTACLES)}
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-1">NPC</label>
          <select name="npc" class="block w-full p-2 rounded-lg border-2 border-dndslate bg-dndnight text-white">
            {options(NPC_TYPES)}
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-1">Goal</label>
          <select name="goal" class="block w-full p-2 rounded-lg border-2 border-dndslate bg-dndnight text-white">
            {options(GOALS)}
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-1">Extra details</label>
          <input name="extra" class="block w-full p-2 rounded-lg border-2 border-dndslate bg-dndnight text-white" placeholder="Any extra details...">
        </div>
      </div>
      <button type="submit" class="mt-4 bg-indigo-600 text-white px-4 py-2 rounded-lg shadow-md hover:bg-indigo-500">Generate Scene</button>
    </form>
    <a href="/" class="text-indigo-400 hover:underline">&larr; Back to Scenes</a>
    '''
    return render_template_string(layout, body=body)

# --- Robust interpret_action for Flask ---
def interpret_action(action, llm, scene_context=None):
    prompt = f"""
You are a D&D rules expert. Given a player action and the current scene context, extract:
- character name (must match a character in the party)
- the most relevant skill to check (must be one of: acrobatics, animal handling, arcana, athletics, deception, history, insight, intimidation, investigation, medicine, nature, perception, performance, persuasion, religion, sleight of hand, stealth, survival; if none apply, use a stat from: strength, dexterity, constitution, intelligence, wisdom, charisma)
- dice to roll (e.g., d20, d6)
Respond ONLY with valid JSON, no explanation, no markdown, no extra text. Example: {{"character": "Aragorn", "stat": "intimidation", "dice": "d20"}}
If you are unsure, make your best guess based on D&D 5e rules.
Scene context: {scene_context or 'None'}
Action: {action}
"""
    response = llm.invoke(prompt)
    import json
    try:
        return json.loads(response.content)
    except Exception:
        return {'error': 'Could not parse LLM response', 'raw': response.content}

@app.route('/action', methods=['GET', 'POST'])
def action():
    result = ''
    if request.method == 'POST':
        char = request.form.get('char')
        action_text = request.form.get('action')
        scene_file = request.form.get('scene_file')
        adv_type = request.form.get('adv_type')
        dc = request.form.get('dc')
        roll_context = request.form.get('roll_context')
        scene_context = load_scene_context(scene_file) if scene_file else None
        interp = interpret_action(action_text, llm, scene_context=scene_context)
        if not interp or 'error' in interp:
            raw = interp.get('raw', '') if interp else ''
            result = f'<span style="color:red">Could not interpret action. Raw LLM output:<br><pre>{raw}</pre></span>'
        else:
            # Always use the user-selected character if provided
            if char:
                interp['character'] = char
            char = interp.get('character')
            stat = interp.get('stat') or interp.get('skill')  # Accept both keys
            dice = interp.get('dice', 'd20')
            if not char or not stat:
                result = f'<span style="color:red">AI did not return a valid character or stat/skill. Raw LLM output:<br><pre>{interp}</pre></span>'
            else:
                _, total_mod, proficiency_bonus, proficient = get_character_stat_and_modifier(char, stat)
                total, rolls, chosen = roll_dice(dice, total_mod, adv_type)
                nat_roll = chosen if dice == 'd20' else (rolls[0] if rolls else None)
                if not dc:
                    dc = 15
                else:
                    try:
                        dc = int(dc)
                    except:
                        dc = 15
                if dice == 'd20' and nat_roll == 20:
                    outcome = 'critical success'
                elif dice == 'd20' and nat_roll == 1:
                    outcome = 'critical failure'
                else:
                    outcome = "success" if total >= dc else "failure"
                narration = narrate_result(
                    action_text, stat, rolls, total_mod, total, dc, outcome, llm,
                    roll_context=roll_context, scene_context=scene_context, character=char
                )
                # Outcome color for highlighting
                outcome_class = {
                    'success': 'border-t-4 border-dndgreen',
                    'failure': 'border-t-4 border-dndred',
                    'critical success': 'border-t-4 border-dndgold',
                    'critical failure': 'border-t-4 border-dndred'
                }.get(outcome.lower(), '')
                icon = {
                    'success': '🗡️',
                    'failure': '💀',
                    'critical success': '✨',
                    'critical failure': '💀'
                }.get(outcome.lower(), '🎲')
                summary = f"<div class='text-lg text-center mb-4'><b>{char}</b> rolled <b>{rolls}</b> ({stat.title()}) — <span class='font-bold'>{outcome.title()}</span>!</div>"
                result = f'''
<div class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50">
  <div class="result-card bg-slate-900 text-slate-100 rounded-2xl shadow-2xl max-w-lg w-full p-6 space-y-4 {outcome_class}">
    <div class="flex items-center gap-2 justify-center text-3xl mb-2">{icon} <span class="font-cinzel text-dndgold">D&D Action Result</span></div>
    {summary}
    <table class="w-full border-separate border-spacing-y-2 text-base mb-4">
      <tbody>
        <tr><td class="text-slate-400 font-semibold w-1/3">Character</td><td>{char}</td></tr>
        <tr><td class="text-slate-400 font-semibold">Stat/Skill</td><td>{stat.title()}</td></tr>
        <tr><td class="text-slate-400 font-semibold">Dice</td><td>{dice}</td></tr>
        <tr><td class="text-slate-400 font-semibold">Rolls</td><td>{rolls}</td></tr>
        <tr><td class="text-slate-400 font-semibold">Modifier</td><td>{total_mod}</td></tr>
        <tr><td class="text-slate-400 font-semibold">Total</td><td>{total}</td></tr>
        <tr><td class="text-slate-400 font-semibold">DC</td><td>{dc}</td></tr>
        <tr class="font-bold"><td class="text-dndgold">Outcome</td><td>{outcome.title()}</td></tr>
      </tbody>
    </table>
    <blockquote class="narration-block bg-slate-800 border-l-4 border-dndgold rounded-lg italic text-dndgold text-lg p-4 mb-2">{narration}</blockquote>
    <button type="button" class="copy-btn border border-indigo-400 text-indigo-300 hover:bg-indigo-600 hover:text-white float-right px-4 py-2 rounded-lg transition" onclick="copyNarration()">Copy Narration</button>
  </div>
</div>
'''
    # GET: show form
    scene_files = sorted(glob.glob(os.path.join(SCENE_DIR, 'scene_*.yaml')))
    char_options = '<option value="Aragorn">Aragorn</option>' # You can expand this
    scene_options = ''.join([f'<option value="{os.path.basename(f)}">{os.path.basename(f)}</option>' for f in scene_files])
    body = f'''
    <div class="bg-dndslate rounded-2xl shadow-2xl p-8 mb-10">
      <h2 class="font-cinzel text-2xl text-dndgold mb-6">D&D Action</h2>
      <form method="post" class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="flex flex-col space-y-1">
          <label for="char" class="font-semibold text-slate-300">Character</label>
          <select name="char" id="char" class="h-12 px-4 rounded-lg bg-dndnight text-slate-100 border border-slate-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
            {char_options}
          </select>
        </div>
        <div class="flex flex-col space-y-1">
          <label for="action" class="font-semibold text-slate-300">Action</label>
          <input name="action" id="action" class="h-12 px-4 rounded-lg bg-dndnight text-slate-100 border border-slate-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 placeholder-slate-500 italic" placeholder="Describe what your character does...">
        </div>
        <div class="flex flex-col space-y-1">
          <label for="scene_file" class="font-semibold text-slate-300">Scene</label>
          <select name="scene_file" id="scene_file" class="h-12 px-4 rounded-lg bg-dndnight text-slate-100 border border-slate-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
            <option value="">[None]</option>{scene_options}
          </select>
        </div>
        <div class="flex flex-col space-y-1">
          <label class="font-semibold text-slate-300">Advantage/Disadvantage</label>
          <input type="hidden" name="adv_type" id="adv_type" value="">
          <div class="inline-flex rounded-lg bg-slate-800 p-1 w-full">
            <button type="button" class="adv-btn px-4 py-2 rounded-l-lg transition-colors" data-value="advantage">Adv</button>
            <button type="button" class="adv-btn px-4 py-2 bg-indigo-500 text-white transition-colors" data-value="">Normal</button>
            <button type="button" class="adv-btn px-4 py-2 rounded-r-lg transition-colors" data-value="disadvantage">Dis</button>
          </div>
        </div>
        <div class="flex flex-col space-y-1">
          <label for="dc" class="font-semibold text-slate-300">DC</label>
          <input name="dc" id="dc" class="h-12 px-4 rounded-lg bg-dndnight text-slate-100 border border-slate-700 focus:outline-none focus:ring-2 focus:ring-indigo-500" placeholder="e.g. 15">
        </div>
        <div class="flex flex-col space-y-1">
          <label for="roll_context" class="font-semibold text-slate-300">Roll context (optional)</label>
          <input name="roll_context" id="roll_context" class="h-12 px-4 rounded-lg bg-dndnight text-slate-100 border border-slate-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 placeholder-slate-500 italic" placeholder="Any extra info for the DM...">
        </div>
        <div class="md:col-span-2 flex justify-end">
          <button type="submit" class="btn-primary bg-indigo-600 hover:bg-indigo-700 text-white font-semibold rounded-lg shadow-lg px-6 py-3 transition w-full md:w-auto">Roll!</button>
        </div>
      </form>
    </div>
    {result}
    <div class="mt-8"><a href="/" class="text-indigo-400 hover:underline text-lg">&larr; Back to Scenes</a></div>'''
    return render_template_string(layout, body=body)

if __name__ == '__main__':
    app.run(debug=True)

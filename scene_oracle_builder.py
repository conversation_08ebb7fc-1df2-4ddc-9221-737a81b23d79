import os
import random
import yaml
from rich.console import Console
from rich.prompt import Prompt
from rich.table import Table
from langchain_openai import Chat<PERSON>penAI
from dotenv import load_dotenv

console = Console()
load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
llm = ChatOpenAI(openai_api_key=OPENAI_API_KEY)

# --- Oracle Tables ---
ENVIRONMENTS = [
    "Dungeon", "Forest", "Mountain Pass", "Village", "Ruins", "Cavern", "Castle", "Desert", "Swamp", "Coast", "Temple", "Roadside"
]
MOODS = [
    "Tense", "Mysterious", "Peaceful", "Chaotic", "Ominous", "Hopeful", "Desperate", "Eerie", "Welcoming", "Foreboding"
]
OBSTACLES = [
    "Locked Door", "Trap", "Monster", "Puzzle", "Rival Adventurers", "Hazardous Terrain", "Magical Barrier", "Weather Event", "Time Pressure", "Moral Dilemma"
]
NPC_TYPES = [
    "Helpful NPC", "Hostile NPC", "Merchant", "Lost Traveler", "Guard", "Noble", "Hermit", "Cultist", "Monster with Motive", "Ghost"
]
GOALS = [
    "Find an item", "Rescue someone", "Escape", "Discover a secret", "Defeat a foe", "Negotiate", "Survive the night", "Solve a mystery", "Deliver a message", "Explore the unknown"
]

# --- Oracle Functions ---
def oracle_roll(table):
    return random.choice(table)

def build_scene_yaml(scene_dict, filename):
    with open(filename, 'w', encoding='utf-8') as f:
        yaml.dump(scene_dict, f, allow_unicode=True, sort_keys=False)
    console.print(f"[green]Scene YAML saved to {filename}[/green]")

def pick_or_roll(table, label):
    while True:
        console.print(f"[bold cyan]{label} options:[/bold cyan] {', '.join(table)}")
        choice = Prompt.ask(f"Pick a {label} or type 'r' to roll randomly", default='r')
        if choice.lower() == 'r':
            result = oracle_roll(table)
            console.print(f"[green]Rolled: {result}[/green]")
            return result
        elif choice in table:
            return choice
        else:
            console.print(f"[red]Invalid choice. Please pick from the list or type 'r'.[/red]")

def clean_ai_yaml(ai_yaml):
    # Remove triple backticks and 'yaml' if present
    lines = ai_yaml.strip().splitlines()
    if lines and lines[0].strip().startswith('```'):
        lines = lines[1:]
    if lines and lines[-1].strip().startswith('```'):
        lines = lines[:-1]
    # Remove 'yaml' after opening backticks if present
    if lines and lines[0].strip().lower() == 'yaml':
        lines = lines[1:]
    return '\n'.join(lines)

# --- Main Scene Builder ---
def main():
    console.print("[bold magenta]--- Scene Oracle & Builder ---[/bold magenta]")
    env = pick_or_roll(ENVIRONMENTS, "Environment")
    mood = pick_or_roll(MOODS, "Mood")
    obstacle = pick_or_roll(OBSTACLES, "Obstacle")
    npc = pick_or_roll(NPC_TYPES, "NPC Type")
    goal = pick_or_roll(GOALS, "Goal")
    # Let user add extra details
    extra = Prompt.ask("Extra details or inspiration (optional)", default="")
    # Use AI to write a scene description and YAML
    prompt = f"""
You are a D&D scene builder. Given the following elements, write a YAML scene file in the same schema as the user's system. Include all relevant fields and a vivid description. Use the following:
- Environment: {env}
- Mood: {mood}
- Obstacle: {obstacle}
- NPC: {npc}
- Goal: {goal}
- Extra: {extra}
Respond ONLY with valid YAML, no explanation.
"""
    ai_yaml = llm.invoke(prompt).content
    ai_yaml_clean = clean_ai_yaml(ai_yaml)
    # Show and save
    console.print("[bold yellow]\nGenerated Scene YAML:[/bold yellow]")
    console.print(ai_yaml_clean)
    filename = Prompt.ask("Filename to save (e.g., scene_new.yaml)", default=f"scene_{env.lower().replace(' ','_')}.yaml")
    # Validate and save YAML
    try:
        scene_dict = yaml.safe_load(ai_yaml_clean)
        build_scene_yaml(scene_dict, filename)
    except Exception as e:
        console.print(f"[red]Error parsing AI YAML: {e}[/red]")
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(ai_yaml_clean)
        console.print(f"[yellow]Raw AI output saved to {filename} for manual review.[/yellow]")

if __name__ == "__main__":
    main()

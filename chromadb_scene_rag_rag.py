import os
import yaml
from dotenv import load_dotenv
from langchain_community.vectorstores import Chroma
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain.schema import Document
import glob
import random
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text

console = Console()

# Load environment variables from .env
load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise RuntimeError("OPENAI_API_KEY not set in .env file!")

# --- Load scene context from YAML ---
def load_scene_context(scene_file):
    if not os.path.exists(scene_file):
        return ""
    with open(scene_file, 'r', encoding='utf-8') as f:
        data = yaml.safe_load(f)
    context = []
    for k, v in data.items():
        context.append(f"{k}: {v}")
    return '\n'.join(context)

# --- Load and segment all YAML files for RAG ---
def load_all_documents(base_dir):
    all_segments = []
    for file_path in glob.glob(os.path.join(base_dir, "**", "*.yaml"), recursive=True):
        doc_name = os.path.splitext(os.path.basename(file_path))[0]
        with open(file_path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        for key, value in data.items():
            section_text = f"{doc_name} {key}: {value}"
            all_segments.append(Document(page_content=section_text, metadata={"doc": doc_name, "section": key}))
    return all_segments

# --- D&D Stat and Skill Aliases ---
DND_STATS = [
    "strength", "dexterity", "constitution", "intelligence", "wisdom", "charisma"
]
DND_SKILLS = [
    "acrobatics", "animal handling", "arcana", "athletics", "deception", "history", "insight", "intimidation", "investigation", "medicine", "nature", "perception", "performance", "persuasion", "religion", "sleight of hand", "stealth", "survival"
]
STAT_ALIASES = {
    "str": "strength", "dex": "dexterity", "con": "constitution", "int": "intelligence", "wis": "wisdom", "cha": "charisma"
}

# --- Stat/Skill/Proficiency Helper ---
def get_character_stat_and_modifier(character_name, stat_or_skill, base_dir='characters'):
    char_dir = os.path.join(base_dir, character_name)
    yaml_path = os.path.join(char_dir, 'sheet.yaml')
    if not os.path.exists(yaml_path):
        return None, 0, 0, False
    with open(yaml_path, 'r', encoding='utf-8') as f:
        data = yaml.safe_load(f)
    key = stat_or_skill.lower().replace("_", " ")
    key = STAT_ALIASES.get(key, key)
    proficiency_bonus = data.get('proficiency_bonus', 0)
    if 'skills' in data and isinstance(data['skills'], dict):
        skills = data['skills']
        if key in skills:
            skill_val = skills[key]
            if isinstance(skill_val, dict):
                mod = skill_val.get('mod', 0)
                proficient = skill_val.get('proficient', False)
                total_mod = mod + (proficiency_bonus if proficient else 0)
                return skill_val, total_mod, proficiency_bonus, proficient
            elif isinstance(skill_val, int):
                return skill_val, skill_val, proficiency_bonus, False
    if key in data and isinstance(data[key], dict) and 'mod' in data[key]:
        mod = data[key]['mod']
        proficient = data[key].get('proficient', False)
        total_mod = mod + (proficiency_bonus if proficient else 0)
        return data[key], total_mod, proficiency_bonus, proficient
    if key in data:
        stat_val = data[key]
        if f"{key}_mod" in data:
            mod = data[f"{key}_mod"]
        elif isinstance(stat_val, dict) and 'mod' in stat_val:
            mod = stat_val['mod']
        elif isinstance(stat_val, int):
            mod = (stat_val - 10) // 2
        else:
            mod = 0
        return stat_val, mod, proficiency_bonus, False
    return None, 0, proficiency_bonus, False

# --- RAG Setup ---
embeddings = OpenAIEmbeddings(openai_api_key=OPENAI_API_KEY)
all_docs = load_all_documents(os.getcwd())
vectorstore = Chroma.from_documents(all_docs, embeddings, persist_directory=".chroma_scene_rag")

# --- RAG Retrieval ---
def retrieve_rag_context(query, k=3):
    results = vectorstore.similarity_search(query, k=k)
    return '\n'.join([doc.page_content for doc in results])

# --- Action Interpreter AI (with RAG context) ---
def interpret_action(action, llm, scene_context=None, rag_context=None):
    prompt = f"""
You are a D&D rules expert. Given a player action, the current scene context, and relevant world/lore context, extract:
- character name (must match a character in the party)
- the most relevant skill to check (must be one of: {', '.join(DND_SKILLS)}; if none apply, use a stat from: {', '.join(DND_STATS)})
- dice to roll (e.g., d20, d6)
Respond as JSON: {{'character': ..., 'stat': ..., 'dice': ...}}
If you are unsure, make your best guess based on D&D 5e rules.
Scene context: {scene_context or 'None'}
RAG context: {rag_context or 'None'}
Action: {action}
"""
    response = llm.invoke(prompt)
    import json
    try:
        return json.loads(response.content)
    except Exception:
        return None

# --- Narrator AI (with RAG context) ---
def narrate_result(action, stat, roll, modifier, total, dc, outcome, llm, roll_context=None, scene_context=None, rag_context=None):
    prompt = f"""
You are a D&D narrator. Narrate the result of this action as a Dungeon Master would, using the following context:
- Action: {action}
- Skill/Stat: {stat}
- Dice roll: {roll}
- Modifier: {modifier}
- Total: {total}
- DC: {dc}
- Outcome: {outcome}
"""
    if roll_context:
        prompt += f"\n- Additional context: {roll_context}"
    if scene_context:
        prompt += f"\n- Scene context: {scene_context}"
    if rag_context:
        prompt += f"\n- RAG context: {rag_context}"
    prompt += "\nIf the outcome is 'critical success', make the narration especially dramatic and positive. If 'critical failure', make it especially dramatic and negative. For regular success or failure, narrate as appropriate. If possible, mention the environment or consequences. Do NOT invent new numbers or fudge the result. The player has already seen the math.\n"
    return llm.invoke(prompt).content

# --- Dice Roller ---
def roll_dice(dice_str, modifier=0, adv_type=None):
    import re
    m = re.match(r"(\d*)d(\d+)", dice_str)
    if not m:
        return 0, []
    num = int(m.group(1)) if m.group(1) else 1
    die = int(m.group(2))
    if adv_type in ('advantage', 'disadvantage') and dice_str == 'd20':
        roll1 = random.randint(1, die)
        roll2 = random.randint(1, die)
        chosen = max(roll1, roll2) if adv_type == 'advantage' else min(roll1, roll2)
        rolls = [roll1, roll2]
        total = chosen + modifier
        return total, rolls, chosen
    else:
        rolls = [random.randint(1, die) for _ in range(num)]
        total = sum(rolls) + modifier
        return total, rolls, rolls[0] if rolls else None

# --- Scene Selection ---
def select_scene_context():
    # Find all scene YAML files matching scene_*.yaml
    scene_files = sorted([f for f in os.listdir('.') if f.startswith('scene_') and f.endswith('.yaml')])
    console.print("[bold yellow]Available scenes:[/bold yellow]")
    for idx, fname in enumerate(scene_files, 1):
        console.print(f"  [cyan]{idx}.[/cyan] {fname}")
    console.print(f"  [cyan]{len(scene_files)+1}.[/cyan] [Type your own scene description]")
    console.print(f"  [cyan]{len(scene_files)+2}.[/cyan] [No scene context]")
    while True:
        choice = console.input("[bold yellow]Pick a scene by number, type a filename, or type your own scene description:[/bold yellow] ").strip()
        if not choice:
            return None, None  # No scene
        if choice.isdigit():
            idx = int(choice)
            if 1 <= idx <= len(scene_files):
                scene_file = scene_files[idx-1]
                return load_scene_context(scene_file), scene_file
            elif idx == len(scene_files)+1:
                # User wants to type their own scene
                scene_text = console.input("[bold yellow]Type your scene description:[/bold yellow] ").strip()
                return scene_text, None
            elif idx == len(scene_files)+2:
                return None, None
            else:
                console.print("[red]Invalid number. Try again.[/red]")
        elif os.path.isfile(choice):
            return load_scene_context(choice), choice
        else:
            # Treat as custom scene description
            return choice, None

# --- Main Interactive Loop ---
def ai_action_loop():
    scene_context, scene_file = select_scene_context()
    console.print("[bold cyan]\nDescribe your action (e.g., 'Aragorn attacks the orc with his sword'):[/bold cyan]")
    while True:
        action = console.input("[bold yellow]Action:[/bold yellow] ")
        if not action.strip():
            console.print("[red]Please enter an action.[/red]")
            continue
        roll_context = console.input("[bold yellow]Describe the situation, reason, or stakes for this roll (optional): [/bold yellow]")
        adv_input = console.input("[bold yellow]Advantage, Disadvantage, or Normal? (a/d/n): [/bold yellow]").strip().lower()
        if adv_input == 'a':
            adv_type = 'advantage'
        elif adv_input == 'd':
            adv_type = 'disadvantage'
        else:
            adv_type = None
        dc_input = console.input("[bold yellow]Enter DC (or leave blank for AI to suggest): [/bold yellow]")
        if dc_input.strip():
            try:
                DC = int(dc_input.strip())
            except Exception:
                console.print("[red]Invalid DC, using 15.[/red]")
                DC = 15
        else:
            prompt = f"You are a D&D DM. Suggest a reasonable DC (number only) for this action: {action}\nScene context: {scene_context or 'None'}"
            dc_resp = vectorstore.as_retriever().llm.invoke(prompt)
            try:
                DC = int(''.join(filter(str.isdigit, dc_resp.content.split()[0])))
            except Exception:
                DC = 15
            console.print(f"[green]AI-suggested DC: {DC}[/green]")
        # --- RAG retrieval for this action ---
        rag_context = retrieve_rag_context(action, k=3)
        interp = interpret_action(action, ChatOpenAI(openai_api_key=OPENAI_API_KEY), scene_context=scene_context, rag_context=rag_context)
        if not interp:
            console.print("[red]Could not interpret action. Try again.[/red]")
            continue
        char = interp.get('character')
        stat = interp.get('stat')
        dice = interp.get('dice', 'd20')
        if not char or not stat:
            console.print(f"[red]Missing character or stat in interpretation: {interp}[/red]")
            continue
        _, total_mod, proficiency_bonus, proficient = get_character_stat_and_modifier(char, stat)
        total, rolls, chosen = roll_dice(dice, total_mod, adv_type)
        nat_roll = chosen if dice == 'd20' else (rolls[0] if rolls else None)
        critical = None
        if dice == 'd20' and nat_roll == 20:
            outcome = 'critical success'
            critical = 'critical success'
        elif dice == 'd20' and nat_roll == 1:
            outcome = 'critical failure'
            critical = 'critical failure'
        else:
            outcome = "success" if total >= DC else "failure"
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("[cyan]Component[/cyan]", justify="right")
        table.add_column("[green]Value[/green]", justify="left")
        table.add_row("Character", f"[bold]{char}[/bold]")
        table.add_row("Skill/Stat", f"[bold]{stat}[/bold]")
        table.add_row("Dice", f"[bold]{dice}[/bold]")
        if adv_type:
            table.add_row("Adv/Disadv", f"[bold]{adv_type.title()}[/bold]")
        table.add_row("Roll(s)", f"[bold]{rolls}[/bold]")
        if adv_type and dice == 'd20':
            table.add_row("Used Roll", f"[bold]{chosen}[/bold]")
        table.add_row("Modifier", f"[bold]{total_mod - (proficiency_bonus if proficient else 0):+}[/bold]")
        if proficient:
            table.add_row("Proficiency", f"[bold]{proficiency_bonus:+}[/bold]")
        table.add_row("Total", f"[bold]{total}[/bold]")
        table.add_row("DC", f"[bold]{DC}[/bold]")
        if critical:
            table.add_row("[yellow]CRITICAL[/yellow]", f"[bold red]{critical.upper()}[/bold red]")
        table.add_row("Outcome", f"[bold green]SUCCESS[/bold green]" if outcome=="success" else ("[bold red]FAILURE[/bold red]" if outcome=="failure" else f"[bold yellow]{outcome.upper()}[/bold yellow]"))
        console.print(Panel(table, title="[bold yellow]Skill/Stat Check[/bold yellow]", border_style="bright_blue"))
        narration = narrate_result(
            action,
            stat,
            rolls,
            total_mod,
            total,
            DC,
            outcome,
            ChatOpenAI(openai_api_key=OPENAI_API_KEY),
            roll_context=roll_context if roll_context.strip() else None,
            scene_context=scene_context,
            rag_context=rag_context
        )
        console.print(Panel(Text(narration, style="bold white"), title="[bold magenta]Narration[/bold magenta]", border_style="magenta"))

if __name__ == "__main__":
    ai_action_loop()

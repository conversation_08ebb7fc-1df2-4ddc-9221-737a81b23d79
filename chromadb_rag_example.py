import chromadb
from chromadb.config import Settings
import yaml
import os
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables from .env
load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise RuntimeError("OPENAI_API_KEY not set in .env file!")
openai_client = OpenAI(api_key=OPENAI_API_KEY)

# Load and segment character sheet YAML into sections and sub-sections
def load_and_segment_character_sheet(path, character_name):
    with open(path, 'r', encoding='utf-8') as f:
        data = yaml.safe_load(f)
    segments = []
    for key, value in data.items():
        if isinstance(value, dict):
            for subkey, subval in value.items():
                section_text = f"{character_name} {key} - {subkey}:\n{yaml.dump(subval, default_flow_style=False)}"
                segments.append({
                    "document": section_text,
                    "metadata": {"character": character_name, "section": f"{key}-{subkey}"}
                })
        elif isinstance(value, list):
            section_text = f"{character_name} {key}: {', '.join(str(i) for i in value)}"
            segments.append({
                "document": section_text,
                "metadata": {"character": character_name, "section": key}
            })
        else:
            section_text = f"{character_name} {key}: {value}"
            segments.append({
                "document": section_text,
                "metadata": {"character": character_name, "section": key}
            })
    return segments

# Paths to character sheets
dir_path = os.path.join(os.getcwd(), 'characters')
aragorn_path = os.path.join(dir_path, 'aragorn', 'sheet.yaml')
elowen_path = os.path.join(dir_path, 'elowen', 'sheet.yaml')

aragorn_segments = load_and_segment_character_sheet(aragorn_path, "Aragorn")
elowen_segments = load_and_segment_character_sheet(elowen_path, "Elowen")

documents = [s["document"] for s in aragorn_segments + elowen_segments]
ids = [f"{s['metadata']['character'].lower()}_{s['metadata']['section']}" for s in aragorn_segments + elowen_segments]
metadatas = [s["metadata"] for s in aragorn_segments + elowen_segments]

client = chromadb.Client(Settings())
collection = client.create_collection(name="dnd_character_sections")
collection.add(
    documents=documents,
    ids=ids,
    metadatas=metadatas
)

print("Type your question about the characters and press Enter (Ctrl+C to exit):")
try:
    while True:
        q = input("Query: ")
        if not q.strip():
            print("Please enter a question.")
            continue
        # 1. Query ChromaDB for top 5 relevant sections
        results = collection.query(query_texts=[q], n_results=5)
        retrieved_sections = []
        for doc, meta in zip(results["documents"][0], results["metadatas"][0]):
            retrieved_sections.append(f"Character: {meta['character']} | Section: {meta['section']}\n{doc}")
        # 2. Send the retrieved sections and the user query to the LLM for synthesis
        context = "\n---\n".join(retrieved_sections)
        prompt = f"""
You are an expert D&D assistant. Given the following user question and character sheet sections, answer as precisely as possible using only the provided data.

User question: {q}

Character sheet sections:
{context}

Answer:
"""
        response = openai_client.chat.completions.create(
            model="gpt-4.1-mini-2025-04-14",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=150,
            temperature=0
        )
        print("\n[AI Answer]:\n" + response.choices[0].message.content.strip() + "\n")
except KeyboardInterrupt:
    print("\nExiting.")
